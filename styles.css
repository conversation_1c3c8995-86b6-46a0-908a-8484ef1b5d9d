* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

body {
    font-family: Arial, sans-serif;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
    max-width: 580px;
    width: 100%;
    animation: fadeIn 0.8s ease;
}

.game-title {
    text-align: center;
    color: #8b5a2b;
    margin-bottom: 15px;
    font-size: 28px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    border-bottom: 2px solid #e0c9a6;
    padding-bottom: 10px;
}

.game-instructions {
    background-color: #f8f8f8;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
    font-size: 14px;
    color: #555;
    border-left: 4px solid #4CAF50;
}

.game-instructions p {
    margin: 5px 0;
}

.game-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: bold;
}

.game-board {
    position: relative;
    width: 540px;
    height: 540px;
    margin: 0 auto 20px;
    border: 6px solid #8b5a2b;
    border-radius: 8px;
    background-color: #e0c9a6;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    background-image: 
        linear-gradient(to right, #8b5a2b 2px, transparent 2px),
        linear-gradient(to bottom, #8b5a2b 2px, transparent 2px);
    background-size: 60px 60px;
    background-position: 30px 30px;
    /* 添加内边框增强视觉效果 */
    outline: 2px solid #654321;
    outline-offset: -3px;
}

.intersection {
    position: absolute;
    width: 20px;
    height: 20px;
    cursor: pointer;
    border-radius: 50%;
    transition: background-color 0.3s ease;
    z-index: 1;
}

.intersection:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.intersection.valid-move {
    background-color: rgba(170, 255, 170, 0.8);
    animation: pulse 2s infinite;
    z-index: 2;
}

.intersection.enemy-attack-zone::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid #e74c3c;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
    pointer-events: none;
    z-index: 3;
}

.intersection.last-move {
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.8);
    z-index: 2;
}

.piece {
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid #333;
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    /* 确保所有棋子都使用相同的居中定位 */
    transform: translate(-50%, -50%);
    z-index: 5;
}

.piece.player {
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    /* 确保玩家棋子有足够的z-index */
    z-index: 6;
}

.piece.enemy {
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    z-index: 5;
}

.piece.selected {
    animation: pulse 1s infinite;
    box-shadow: 0 0 15px gold;
    z-index: 7;
}

.piece.defeated {
    animation: rotate 1s ease;
    transform: scale(1.5);
    z-index: 10;
}

.piece.moving {
    transition: transform 0.5s ease;
}

/* 不同棋子类型的不同背景颜色 */
.piece-pawn.player {
    background: linear-gradient(135deg, #ff9a9e, #fecfef) !important;
    color: #2c3e50 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #e84393 !important;
}

.piece-pawn.enemy {
    background: linear-gradient(135deg, #a8edea, #fed6e3) !important;
    color: #2c3e50 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #00b894 !important;
}

.piece-advisor.player {
    background: linear-gradient(135deg, #ffecd2, #fcb69f) !important;
    color: #8b4513 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #e17055 !important;
}

.piece-advisor.enemy {
    background: linear-gradient(135deg, #a8caba, #5d4e75) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.piece-knight.player {
    background: linear-gradient(135deg, #fad0c4, #ffd1ff) !important;
    color: #663399 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #a29bfe !important;
}

.piece-knight.enemy {
    background: linear-gradient(135deg, #a1c4fd, #c2e9fb) !important;
    color: #1e3c72 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #0984e3 !important;
}

.piece-elephant.player {
    background: linear-gradient(135deg, #f093fb, #f5576c) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #e84393 !important;
}

.piece-elephant.enemy {
    background: linear-gradient(135deg, #4facfe, #00f2fe) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #0984e3 !important;
}

.piece-rook.player {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #d63031 !important;
}

.piece-rook.enemy {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.piece-cannon.player {
    background: linear-gradient(135deg, #f7971e, #ffd200) !important;
    color: #8b4513 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #e17055 !important;
}

.piece-cannon.enemy {
    background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.piece-general.player {
    background: linear-gradient(135deg, #fd79a8, #fdcb6e) !important;
    color: #2d3436 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 3px solid #d63031 !important;
}

.piece-general.enemy {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 3px solid #2d3436 !important;
}

.piece-legend {
    margin-top: 20px;
    background-color: #f8f8f8;
    border-radius: 5px;
    padding: 15px;
}

.piece-legend h3 {
    text-align: center;
    margin-bottom: 10px;
    color: #333;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
}

.legend-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 5px;
}

.legend-piece {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    background-color: #5555ff;
    color: white;
    border: 2px solid #3333cc;
    margin-bottom: 5px;
    font-size: 16px;
}

/* 更新图例棋子以匹配 */
.legend-piece.pawn {
    background: linear-gradient(135deg, #a8edea, #fed6e3) !important;
    color: #2c3e50 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #00b894 !important;
}

.legend-piece.advisor {
    background: linear-gradient(135deg, #a8caba, #5d4e75) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.legend-piece.knight {
    background: linear-gradient(135deg, #a1c4fd, #c2e9fb) !important;
    color: #1e3c72 !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.5) !important;
    border: 2px solid #0984e3 !important;
}

.legend-piece.elephant {
    background: linear-gradient(135deg, #4facfe, #00f2fe) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #0984e3 !important;
}

.legend-piece.rook {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.legend-piece.cannon {
    background: linear-gradient(135deg, #74b9ff, #0984e3) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 2px solid #2d3436 !important;
}

.legend-piece.general {
    background: linear-gradient(135deg, #6c5ce7, #a29bfe) !important;
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7) !important;
    border: 3px solid #2d3436 !important;
}

.game-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 15px;
}

#sound-toggle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 20px;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #4CAF50;
}

#sound-toggle.sound-off {
    background-color: #ccc;
}

button {
    padding: 10px 20px;
    font-size: 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #45a049;
}

#assist-button {
    background-color: #ff9800;
}

#assist-button:hover {
    background-color: #e68900;
}

#assist-button.active {
    background-color: #4CAF50;
}

#assist-button.active:hover {
    background-color: #45a049;
}

.game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

.game-over-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    max-width: 500px;
    width: 100%;
    animation: slideIn 0.5s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close-button {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-button:hover {
    background-color: #f0f0f0;
    color: #333;
}

.game-over h2 {
    margin-bottom: 20px;
    font-size: 24px;
}

.game-over p {
    margin-bottom: 15px;
    font-size: 18px;
}

.hidden {
    display: none;
}

.game-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 18px;
    z-index: 1000;
    animation: slideIn 0.5s ease;
    transition: opacity 0.5s ease;
}

.game-notification.hidden {
    opacity: 0;
    pointer-events: none;
}

.defeat-info {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f8f8f8;
    border-radius: 8px;
    padding: 15px;
    border: 2px solid #e74c3c;
}

.defeat-info p {
    font-size: 16px;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 10px;
}

.defeating-piece {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.defeating-piece .piece {
    width: 80px;
    height: 80px;
    font-size: 32px;
    animation: pulse 2s infinite;
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.8);
    margin-bottom: 10px;
}

.defeating-piece::after {
    content: "击败了你！";
    font-size: 14px;
    color: #e74c3c;
    font-weight: bold;
}

.capture-effect {
    position: fixed;
    color: gold;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 0 5px black;
    z-index: 1000;
    pointer-events: none;
    transition: all 1s ease;
}

@keyframes highlight {
    0% { box-shadow: 0 0 5px 2px gold; }
    50% { box-shadow: 0 0 15px 5px gold; }
    100% { box-shadow: 0 0 5px 2px gold; }
}

/* 商店样式 */
.shop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 100;
}

/* 当商店不隐藏时显示 */
.shop:not(.hidden) {
    display: flex !important;
}

.shop-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.5s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    position: relative;
}

.shop-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
}

.shop-header h2 {
    margin-bottom: 10px;
    color: #333;
}

.shop-header p {
    font-size: 18px;
    font-weight: bold;
    color: #4CAF50;
}

.shop-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.shop-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

.shop-item:hover {
    border-color: #4CAF50;
    background-color: #f0f8f0;
}

.piece-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.piece-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.piece-details div {
    font-size: 14px;
    color: #666;
}

.upgrade-btn {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.upgrade-btn:hover:not(:disabled) {
    background-color: #45a049;
}

.upgrade-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.total-score-container {
    color: #4CAF50;
    font-weight: bold;
}
