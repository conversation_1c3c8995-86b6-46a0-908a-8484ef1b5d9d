# lonelyHorseman

这是一个基于网页的策略游戏项目，玩家在游戏中控制一个棋子，通过移动和升级来击败敌人并获得高分。

## 介绍

本项目是一个单人网页游戏，灵感来源于经典的棋盘游戏机制。玩家需要在有限的棋盘上控制一个棋子，通过策略性移动和升级来击败敌人并尽可能获得更高的分数。

## 软件架构

项目由三个主要文件构成：
- `index.html`：定义了游戏的页面结构和布局。
- `styles.css`：提供了游戏界面的样式和视觉效果。
- `script.js`：实现了游戏的核心逻辑和交互功能。

## 安装教程

1. 克隆仓库到本地：
   ```bash
   git clone https://gitee.com/newway1/lonely-horseman.git
   ```
2. 打开项目文件夹：
   ```bash
   cd lonely-horseman
   ```
3. 在浏览器中打开 `index.html` 文件即可开始游戏。

## 使用说明

- 点击棋盘上的单元格来移动你的棋子。
- 每次移动后，敌人会进行回合行动。
- 通过点击“商店”按钮进入商店，升级你的棋子。
- 游戏结束时，可以选择重新开始。

## 参与贡献

欢迎贡献代码或提出建议！请遵循以下步骤：
1. Fork 本仓库。
2. 创建新分支 (`git checkout -b feature/new-feature`)。
3. 提交更改 (`git commit -m 'Add new feature'`)。
4. 推送至远程分支 (`git push origin feature/new-feature`)。
5. 创建 Pull Request。

## 特技

- 简洁直观的用户界面。
- 动态的棋子移动和战斗逻辑。
- 可扩展的升级系统，支持未来添加更多功能。

## 许可证

本项目采用 MIT 许可证。详情请查看 `LICENSE` 文件。